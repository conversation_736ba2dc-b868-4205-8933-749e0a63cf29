---
import "@fontsource/montserrat/200.css"
import "@fontsource/montserrat/800.css"
import { ClientRouter } from "astro:transitions"
import Footer from "../components/Footer.astro"
import Header from "../components/Header.astro"
import SiteMeta from "../components/SiteMeta.astro"

// Import styles in defined order (use new lines to prevent auto-sorting)
import "../styles/tailwind.css"

import "../assets/scss/index.scss"

const {
  title = "Understand Legacy Code",
  description = "Change messy code without breaking it.",
  url = Astro.site,
  image = "social-preview-image.png",
  author = "<PERSON>",
} = Astro.props
---

<html lang="en" dir="ltr">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />

    <link rel="icon" type="image/png" href="/favicon.png" />

    <link
      rel="alternate"
      type="application/rss+xml"
      title="Understand Legacy Code"
      href="https://understandlegacycode.com/rss.xml"
    />

    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <SiteMeta title={title} description={description.substring(0, 100)} url={url} image={image} author={author} />

    <!-- Enable Astro View Transitions for all browsers -->
    <ClientRouter />
  </head>
  <body class="mx-auto max-w-2xl px-5 py-10">
    <Header />
    <main id="main-content" transition:animate="fade">
      <slot />
    </main>
    <Footer />
    <style lang="scss" is:global>
      @media (min-width: 900px) {
        body {
          background-image: url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 0h2v20H9V0zm25.134.84l1.732 1-10 17.32-1.732-1 10-17.32zm-20 20l1.732 1-10 17.32-1.732-1 10-17.32zM58.16 4.134l1 1.732-17.32 10-1-1.732 17.32-10zm-40 40l1 1.732-17.32 10-1-1.732 17.32-10zM80 9v2H60V9h20zM20 69v2H0v-2h20zm79.32-55l-1 1.732-17.32-10L82 4l17.32 10zm-80 80l-1 1.732-17.32-10L2 84l17.32 10zm96.546-75.84l-1.732 1-10-17.32 1.732-1 10 17.32zm-100 100l-1.732 1-10-17.32 1.732-1 10 17.32zM38.16 24.134l1 1.732-17.32 10-1-1.732 17.32-10zM60 29v2H40v-2h20zm19.32 5l-1 1.732-17.32-10L62 24l17.32 10zm16.546 4.16l-1.732 1-10-17.32 1.732-1 10 17.32zM111 40h-2V20h2v20zm3.134.84l1.732 1-10 17.32-1.732-1 10-17.32zM40 49v2H20v-2h20zm19.32 5l-1 1.732-17.32-10L42 44l17.32 10zm16.546 4.16l-1.732 1-10-17.32 1.732-1 10 17.32zM91 60h-2V40h2v20zm3.134.84l1.732 1-10 17.32-1.732-1 10-17.32zm24.026 3.294l1 1.732-17.32 10-1-1.732 17.32-10zM39.32 74l-1 1.732-17.32-10L22 64l17.32 10zm16.546 4.16l-1.732 1-10-17.32 1.732-1 10 17.32zM71 80h-2V60h2v20zm3.134.84l1.732 1-10 17.32-1.732-1 10-17.32zm24.026 3.294l1 1.732-17.32 10-1-1.732 17.32-10zM120 89v2h-20v-2h20zm-84.134 9.16l-1.732 1-10-17.32 1.732-1 10 17.32zM51 100h-2V80h2v20zm3.134.84l1.732 1-10 17.32-1.732-1 10-17.32zm24.026 3.294l1 1.732-17.32 10-1-1.732 17.32-10zM100 109v2H80v-2h20zm19.32 5l-1 1.732-17.32-10 1-1.732 17.32 10zM31 120h-2v-20h2v20z' fill='%239C92AC' fill-opacity='0.4' fill-rule='evenodd'/%3E%3C/svg%3E");
          background-repeat: repeat-y;
        }

        .darkmode {
          body {
            background-image: url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 0h2v20H9V0zm25.134.84l1.732 1-10 17.32-1.732-1 10-17.32zm-20 20l1.732 1-10 17.32-1.732-1 10-17.32zM58.16 4.134l1 1.732-17.32 10-1-1.732 17.32-10zm-40 40l1 1.732-17.32 10-1-1.732 17.32-10zM80 9v2H60V9h20zM20 69v2H0v-2h20zm79.32-55l-1 1.732-17.32-10L82 4l17.32 10zm-80 80l-1 1.732-17.32-10L2 84l17.32 10zm96.546-75.84l-1.732 1-10-17.32 1.732-1 10 17.32zm-100 100l-1.732 1-10-17.32 1.732-1 10 17.32zM38.16 24.134l1 1.732-17.32 10-1-1.732 17.32-10zM60 29v2H40v-2h20zm19.32 5l-1 1.732-17.32-10L62 24l17.32 10zm16.546 4.16l-1.732 1-10-17.32 1.732-1 10 17.32zM111 40h-2V20h2v20zm3.134.84l1.732 1-10 17.32-1.732-1 10-17.32zM40 49v2H20v-2h20zm19.32 5l-1 1.732-17.32-10L42 44l17.32 10zm16.546 4.16l-1.732 1-10-17.32 1.732-1 10 17.32zM91 60h-2V40h2v20zm3.134.84l1.732 1-10 17.32-1.732-1 10-17.32zm24.026 3.294l1 1.732-17.32 10-1-1.732 17.32-10zM39.32 74l-1 1.732-17.32-10L22 64l17.32 10zm16.546 4.16l-1.732 1-10-17.32 1.732-1 10 17.32zM71 80h-2V60h2v20zm3.134.84l1.732 1-10 17.32-1.732-1 10-17.32zm24.026 3.294l1 1.732-17.32 10-1-1.732 17.32-10zM120 89v2h-20v-2h20zm-84.134 9.16l-1.732 1-10-17.32 1.732-1 10 17.32zM51 100h-2V80h2v20zm3.134.84l1.732 1-10 17.32-1.732-1 10-17.32zm24.026 3.294l1 1.732-17.32 10-1-1.732 17.32-10zM100 109v2H80v-2h20zm19.32 5l-1 1.732-17.32-10 1-1.732 17.32 10zM31 120h-2v-20h2v20z' fill='%23936dd1' fill-opacity='0.6' fill-rule='evenodd'/%3E%3C/svg%3E");
            background-repeat: repeat-y;
          }
        }
      }

      // sticky footer on low content pages
      html,
      body {
        height: 100%;
      }

      body {
        display: flex;
        flex-direction: column;
        overflow-x: clip;

        main {
          flex: 1 0 auto;
        }

        footer {
          flex-shrink: 0;
        }
      }

      pre {
        border: 2px solid var(--link-color);
        border-radius: 0.35rem;
        padding: 1rem;
      }

      // Legacy Code specific styles
      body {
        border-top: 5px solid hsla(280, 85%, 55%, 1);
        min-height: 100vh;
        font-family: "Open Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
      }

      h1,
      h2,
      h3,
      blockquote,
      .title-font {
        font-family: "Space Grotesk", "Open Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
      }

      code {
        background-color: var(--brand-background-lightest, #f9f9f9);
      }

      del {
        text-decoration-style: double;
        text-decoration-thickness: from-font;
      }

      ul,
      ol {
        margin-left: 1.75rem;
      }

      p + ul,
      p + ol {
        margin-top: -0.75rem;
      }

      ul > li,
      ol > li {
        position: relative;
        margin-left: 20px;
        list-style-type: none;
      }

      ol {
        counter-reset: step-counter;
        padding-left: 20px;
      }

      ol > li {
        counter-increment: step-counter;
      }

      ul > li::before {
        display: inline-block;
        position: absolute;
        top: 13px;
        left: -26px;
        transform: rotate(-5deg);
        background: var(--brand-primary, #ed1b2e);
        width: 14px;
        height: 5px;
        content: " ";
      }

      ol > li::before {
        display: block;
        position: absolute;
        top: 8px;
        box-sizing: border-box;
        margin-top: -3px;
        margin-right: 1em;
        margin-left: -62px;
        box-shadow: 0.2em 0.2em 0 rgba(128, 128, 128, 0.2);
        background-color: var(--brand-primary, #ed1b2e);
        padding: 2px 0;
        width: 2.7em;
        height: 1.2em;
        content: counter(step-counter);
        color: white;
        font-style: normal;
        font-size: 0.9em;
        line-height: 1;
        font-family: sharp-sans, sans-serif;
        font-variant-numeric: lining-nums;
        font-feature-settings: "lnum";
        text-align: center;
      }

      ul > li:nth-of-type(2n + 1)::before,
      ol > li:nth-of-type(2n + 1)::before {
        transform: rotate(4deg);
      }

      a {
        transition: box-shadow 0.2s;
        box-shadow: inset 0 -2px 0 var(--brand-primary, #ed1b2e);
        background: 0 0;
        padding: 1px 0 0 0;
        color: inherit;
        text-decoration: none;
      }

      a:hover,
      a:focus,
      a:active {
        box-shadow: inset 0 -1.3em 0 var(--brand-background, #f3f7f9);
      }

      small {
        font-size: 75%;
      }

      blockquote > p {
        font-size: 1.5em;
        line-height: 1.3em;
      }

      @media (min-width: 600px) {
        blockquote {
          margin-top: 3rem;
          margin-bottom: 3rem;
        }
      }

      @media (min-width: 1100px) {
        blockquote {
          position: relative;
          border-color: transparent;
        }

        blockquote::after {
          position: absolute;
          top: 0.15em;
          left: -0.3em;
          content: "“";
          color: hsla(280, 85%, 55%, 0.7);
          font-size: 5em;
          font-family: "Helvetica Neue", Helvetica, serif;
        }
      }
    </style>
  </body>
</html>
