---
import DefaultLayout from "@layouts/DefaultLayout.astro"

export async function getStaticPaths() {
  // Get all blog posts from the content/blog directory
  const allPosts = await Astro.glob("../../../content/blog/**/index.md")

  return allPosts.map((post) => {
    // Extract slug from the file path
    const pathParts = post.file.split("/")
    const slug = pathParts[pathParts.length - 2] // Get the directory name

    return {
      params: { post: slug },
      props: { post },
    }
  })
}

const { post } = Astro.props
const { Content } = post
const { title, date, description, image } = post.frontmatter

const author = {
  name: "<PERSON>",
  image: "/assets/profile-pic.png",
  bio: "Software developer who lives and works in Montreal, Canada 🍁",
}

// Helper function to format date
function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  })
}
---

<DefaultLayout title={title} description={description}>
  <article class="my-12">
    <div class="narrow container">
      <header class="mb-8">
        <h1 class="mb-4 text-4xl font-bold">{title}</h1>
        <div class="mb-4 text-gray-600">
          <time datetime={date}>{formatDate(date)}</time>
        </div>
        {image && <img src={image} alt={title} class="mb-6 h-64 w-full rounded-lg object-cover" />}
        {description && <p class="mb-6 text-xl text-gray-700">{description}</p>}
      </header>

      <div class="prose prose-lg max-w-none">
        <Content />
      </div>

      <footer class="mt-12 border-t border-gray-200 pt-8">
        <div class="flex items-center">
          <img src={author.image} alt={author.name} class="mr-4 h-12 w-12 rounded-full" />
          <div>
            <p class="font-semibold">{author.name}</p>
            <p class="text-sm text-gray-600">{author.bio}</p>
          </div>
        </div>
      </footer>
    </div>
  </article>
</DefaultLayout>
