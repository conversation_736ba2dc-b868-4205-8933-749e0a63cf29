---
import DefaultLayout from "@layouts/DefaultLayout.astro"
import PageHeader from "@components/PageHeader.astro"
import SocialShares from "@components/SocialShares.astro"
import BreakoutImage from "@components/BreakoutImage.astro"
import { Image } from "astro:assets"

export async function getStaticPaths() {
  const data = await fetch("https://jsonplaceholder.typicode.com/posts").then((response) => response.json())

  const postImages = [
    { src: "/posts/post-image-1.png" },
    { src: "/posts/post-image-2.png" },
    { src: "/posts/post-image-3.png" },
    { src: "/posts/post-image-4.png" },
    { src: "/posts/post-image-5.png" },
    { src: "/posts/post-image-6.png" },
  ]

  function truncateTitle(title) {
    const words = title.split(" ")
    const truncated = words.slice(0, 4).join(" ")
    return {
      full: title,
      truncated: truncated,
    }
  }

  return data.map((post) => {
    const titleObj = truncateTitle(post.title)

    const postWithImage = {
      ...post,
      featuredImage: postImages[post.id % postImages.length].src,
      title: titleObj.truncated,
    }

    return {
      params: { post: titleObj.truncated.replaceAll(" ", "-").toLowerCase() },
      props: { post: postWithImage },
    }
  })
}

const { post } = Astro.props
const author = {
  name: "A11Y Person",
  image: "/posts/post-image-1.png",
  bio: "Pasionate accessibility advocate",
}
---

<DefaultLayout title={post.title} description={post.body} url={post.title}>
  <PageHeader
    title={post.title}
    subtitle={post.body}
    author={author}
    bgType="bordered"
    featuredImage={post.featuredImage}
  />
  <section class="my-12">
    <div class="narrow container">
      <p class="text-2xl">{post.body}</p>
    </div>
  </section>
  <section class="my-12">
    <div class="narrow space-content container">
      <h2>Demo content</h2>
      <p>
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos. Lorem ipsum dolor sit amet consectetur
        adipisicing elit. Quisquam, quos. Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.
      </p>
      <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.</p>
      <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
        <Image
          src="/posts/post-image-1.png"
          alt=""
          width={1200}
          height={600}
          class="h-[250px] w-full rounded-lg object-cover"
        />
        <Image
          src="/posts/post-image-2.png"
          alt=""
          width={1200}
          height={600}
          class="h-[250px] w-full rounded-lg object-cover"
        />
        <Image
          src="/posts/post-image-3.png"
          alt=""
          width={1200}
          height={600}
          class="h-[250px] w-full rounded-lg object-cover"
        />
        <Image
          src="/posts/post-image-4.png"
          alt=""
          width={1200}
          height={600}
          class="h-[250px] w-full rounded-lg object-cover"
        />
      </div>
      <p>
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos. Lorem ipsum dolor sit amet consectetur
        adipisicing elit. Quisquam, quos. Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos. Lorem
        ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos. Lorem ipsum dolor sit amet consectetur
        adipisicing elit. Quisquam, quos. Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.
      </p>
      <h3>Demo content</h3>
      <p>
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos. Lorem ipsum dolor sit amet consectetur
        adipisicing elit. Quisquam, quos. Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.
      </p>
      <ul class="list-inside list-disc">
        <li>Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.</li>
        <li>Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.</li>
        <li>Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.</li>
        <li>Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.</li>
        <li>Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.</li>
      </ul>
      <h3>Breakout Image</h3>
      <p>
        This image breaks out of the narrow container on larger screens, creating a more immersive visual experience.
      </p>
      <BreakoutImage src="/posts/post-image-2.png" />
      <p>
        Notice how the image above extends beyond the boundaries of the text content, creating visual interest and
        drawing attention to important visual elements.
      </p>
      <h3>Demo content</h3>
      <p>
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos. Lorem ipsum dolor sit amet consectetur
        adipisicing elit. Quisquam, quos. Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos. Lorem
        ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos. Lorem ipsum dolor sit amet consectetur
        adipisicing elit. Quisquam, quos. Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.
      </p>
    </div>
  </section>
  <section class="my-12">
    <div class="narrow space-content container">
      <h2>Share this post</h2>
      <p>Like this post? Share it with your friends!</p>
      <SocialShares />
    </div>
  </section>
</DefaultLayout>
