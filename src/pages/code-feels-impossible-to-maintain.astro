---
import GuideLayout from "@layouts/GuideLayout.astro"
---

<GuideLayout
  title="Code feels impossible to maintain"
  description="Sometimes, you seem to hit a point of no return and that the best strategy would be to burn it all and start over. Are there alternatives?"
  tag="impossible to maintain"
>
  <h1>Code feels impossible to maintain</h1>
  
  <p>
    Sometimes, you reach a point where the codebase feels completely
    unmaintainable. Every change breaks something else, and you start
    thinking that a complete rewrite would be easier.
  </p>
  
  <p>
    But rewrites are risky and expensive. Are there alternatives? Can you
    gradually improve the situation instead?
  </p>
  
  <p>
    Let's explore techniques to rescue even the most challenging codebases.
  </p>
</GuideLayout>
