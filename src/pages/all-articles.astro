---
import DefaultLayout from "@layouts/DefaultLayout.astro"
import Bio from "@components/Bio.astro"

// Get all blog posts from the content/blog directory
const allPosts = await Astro.glob("../../content/blog/**/index.md")

// Sort posts by date (newest first)
const sortedPosts = allPosts.sort(
  (a, b) => new Date(b.frontmatter.date).getTime() - new Date(a.frontmatter.date).getTime()
)

// Helper function to extract slug from file path
function getSlugFromFile(filePath: string): string {
  const pathParts = filePath.split("/")
  return pathParts[pathParts.length - 2] // Get the directory name
}
---

<DefaultLayout
  title="Understand Legacy Code (all articles)"
  description="Change messy code without breaking it."
>
  <article class="my-12">
    <div class="container">
      <header class="mb-8">
        <h2 class="text-3xl font-bold mb-4">
          <span role="img" aria-label="Lightbulb">💡</span>{" "}
          All articles
        </h2>
      </header>
      
      <ul class="articles-list">
        {
          sortedPosts.map((post) => {
            const title = post.frontmatter.title || "Untitled"
            const slug = getSlugFromFile(post.file)
            return (
              <li class="article-item">
                <a href={`/blog/${slug}`} class="large-link">
                  {title}
                </a>
                <p set:html={post.frontmatter.description || post.compiledContent()} />
              </li>
            )
          })
        }
      </ul>
      
      <hr class="my-12" />
      
      <Bio />
    </div>
  </article>
</DefaultLayout>

<style>
  .container {
    max-width: 768px;
    margin: auto;
    padding: 2rem;
  }

  @media (min-width: 768px) {
    .container {
      padding-left: 0;
      padding-right: 0;
    }
  }

  .articles-list {
    margin: 20px 0 40px;
  }

  .article-item {
    margin-bottom: 2rem;
  }

  .article-item::before {
    top: 16px;
  }

  .large-link {
    font-size: 21px;
    font-weight: bold;
    text-decoration: none;
    display: block;
    margin-bottom: 0.5rem;
  }

  .large-link:hover {
    text-decoration: underline;
  }

  .article-item p {
    margin-bottom: 0;
    line-height: 1.6;
  }
</style>
