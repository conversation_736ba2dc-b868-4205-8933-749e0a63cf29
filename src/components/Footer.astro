---
// import CallToAction from "./CallToAction.astro"
// import ExternalLink from "./ExternalLink.astro"
// import Logo from "./Logo.astro"

/**
 * Footer Component
 *
 * @description A component that displays the footer of the website
 */
const currentYear = new Date().getFullYear()
---

<footer>
  <!-- <CallToAction />
  <section class="py-20">
    <h2 class="sr-only">Footer</h2>
    <div class="container grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
      <div class="flex flex-col gap-4">
        <h3 class="text-2xl font-bold">Navigation</h3>
        <ul class="flex flex-col gap-2">
          <li><a href="/">Home</a></li>
          <li><a href="/blog">Blog</a></li>
          <li><a href="/portfolio">Portfolio</a></li>
        </ul>
      </div>
      <div class="flex flex-col gap-4">
        <h3 class="text-2xl font-bold">Theme features</h3>
        <ul class="flex flex-col gap-2">
          <li><a href="/accessibility-statement">Accessibility statement</a></li>
          <li><a href="/accessible-components">Accessible components</a></li>
          <li><a href="/color-contrast-checker">Color contrast checker</a></li>
          <li><a href="/markdown-page">Markdown page</a></li>
          <li><a href="/mdx-page">MDX page</a></li>
          <li><a href="/404-page">404 page</a></li>
        </ul>
      </div>
      <div class="flex flex-col gap-4">
        <h3 class="text-2xl font-bold">Developer tools</h3>
        <ul class="flex flex-col gap-2">
          <li>
            <ExternalLink href="https://www.w3.org/WAI/ARIA/apg/">ARIA Authoring Practices</ExternalLink>
          </li>
          <li>
            <ExternalLink href="https://web.dev/articles/focus">Focus Management</ExternalLink>
          </li>
          <li>
            <ExternalLink href="https://developer.mozilla.org/en-US/docs/Learn/Accessibility/HTML"
              >Semantic HTML Guide</ExternalLink
            >
          </li>
          <li>
            <ExternalLink href="https://www.tpgi.com/screen-reader-user-survey-results/"
              >Screen Reader Survey</ExternalLink
            >
          </li>
        </ul>
      </div>
      <div class="flex flex-col gap-4">
        <Logo loading="lazy" />
        <p class="leading-[1.75]">
          Tip: always test for screen reader support. Press <kbd>⌘</kbd> + <kbd>F5</kbd> to start VoiceOver or <kbd
            >Win</kbd
          >
          + <kbd>Ctrl</kbd> + <kbd>Enter</kbd> for Narrator.
        </p>
      </div>
    </div>
  </section>
  <section class="py-8">
    <div class="container flex flex-col gap-4 md:flex-row md:justify-between">
      <p>
        &copy; {currentYear} - Starter Theme for <a href="https://astro.build/">Astro</a>.
      </p>
      <p>
        Made with ❤️ by <a href="https://github.com/markteekman">Mark Teekman</a>. Part of <a
          href="https://www.incluud.dev">Incluud</a
        >.
      </p>
    </div>
  </section> -->
</footer>

<style>
  section {
    border-top: 1px solid var(--border-color-subtle);
  }
</style>
