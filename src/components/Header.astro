---
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>Link } from "accessible-astro-components"
import { Icon } from "astro-icon/components"
import Navigation from "../components/Navigation.astro"

/**
 * Header Component
 *
 * @description A component that displays the header of the website
 */
---

<header>
  <SkipLink />
  <Navigation>
    <li class="menu-item">
      <a href="/">Home</a>
    </li>
    <!--
    <li class="menu-item">
      <a href="/blog/">Blog</a>
    </li>
    <li class="menu-item">
      <a href="/portfolio/">Portfolio</a>
    </li>
    <li class="menu-item has-dropdown">
      <button aria-haspopup="true" aria-expanded="false">
        Theme features
        <Icon name="lucide:chevron-down" size="32" />
      </button>
      <ul class="dropdown-menu">
        <li class="submenu-item">
          <a href="/accessibility-statement">Accessibility statement</a>
        </li>
        <li class="submenu-item">
          <a href="/accessible-components">Accessible components</a>
        </li>
        <li class="submenu-item">
          <a href="/color-contrast-checker">Color contrast checker</a>
        </li>
        <li class="submenu-item">
          <a href="/markdown-page/">Markdown page</a>
        </li>
        <li class="submenu-item">
          <a href="/mdx-page/">MDX page</a>
        </li>
        <li class="submenu-item">
          <a href="/404-page">404 page</a>
        </li>
      </ul>
    </li>
    <li class="menu-item highlight">
      <a href="https://accessible-astro.incluud.dev/">Docs </a>
    </li>
    <li class="menu-item type-icon animate-rotate">
      <a href="https://github.com/incluud/accessible-astro-starter">
        <Icon name="lucide:github" />
        <span class="sr-only">Go to the GitHub page</span>
      </a>
    </li> -->
    <li class="menu-item type-icon animate-rotate">
      <DarkMode>
        <Icon name="lucide:moon" slot="light" />
        <Icon name="lucide:sun" slot="dark" />
      </DarkMode>
    </li>
  </Navigation>
</header>

<style lang="scss" is:global>
  header {
    .type-icon {
      display: block;
      margin-inline: -7px;

      svg {
        inline-size: 30px;
        block-size: 30px;
      }
    }
  }
</style>
