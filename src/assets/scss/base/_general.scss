@use "./mixins" as *;

@view-transition {
  navigation: auto;
}

body {
  font-weight: 400;
  font-size: 1.15rem;
  line-height: 1.5;
  font-family: var(--font-body);
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  text-shadow: rgba(0, 0, 0, 0.01) 0 0 1px;
  -webkit-text-size-adjust: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  background-color: var(--background-color);
  color: var(--foreground-color);
}

a {
  transition: color var(--animation-speed-fast) var(--cubic-bezier);
  color: var(--link-color);

  &:where(:hover, :focus-visible) {
    color: var(--link-hover-color);
  }
}

main a,
footer a {
  @include text-decoration(var(--link-color), var(--link-hover-color));

  color: var(--link-color);

  &:where(:hover, :focus-visible) {
    color: var(--link-hover-color);
  }

  &.external-link {
    display: inline-flex;
    align-items: center;
    gap: var(--space-3xs);

    // small visual correction
    svg {
      margin-block-start: 3px;
    }
  }
}

*:focus,
*:focus-visible {
  @include outline;
}

*:focus:not(:focus-visible) {
  outline: none;
  box-shadow: none;
}
